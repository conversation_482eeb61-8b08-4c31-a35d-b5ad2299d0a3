# Sleep Management Module

## Overview
The Sleep Management module provides comprehensive sleep tracking, alarm management, and sleep disruption monitoring for GPAce. This module helps users maintain healthy sleep schedules and track factors that may interfere with their sleep quality.

## Features
- **Smart Alarm System**: Create, manage, and schedule alarms with various options
- **Sleep Schedule Tracking**: Monitor and display current time with customizable formats
- **Sleep Saboteurs Monitoring**: Track and identify factors that disrupt sleep patterns
- **Multi-storage Support**: Data persistence across localStorage, cache, and Firestore
- **Real-time Synchronization**: Cross-device sync when authenticated

## Module Structure

```
features/sleep-management/
├── alarms/                     # Alarm system components
│   ├── alarm-service.js        # Main alarm service with CRUD operations
│   ├── alarm-handler.js        # Alarm event handling and notifications
│   ├── alarm-data-service.js   # Data persistence and synchronization
│   ├── alarm-mini-display.js   # Compact alarm display widget
│   └── alarm-service-worker.js # Background alarm processing
├── schedule/                   # Sleep schedule management
│   └── clock-display.js        # Real-time clock with format options
├── saboteurs/                  # Sleep disruption tracking
│   └── sleep-saboteurs-init.js # Initialization for sleep tracking
└── ui/                         # User interface components
    ├── sleep-saboteurs.html    # Main sleep management interface
    ├── alarm-service.css       # Alarm system styling
    └── sleep-saboteurs.css     # Sleep tracking interface styling
```

## Core Components

### Alarm Service (`alarms/alarm-service.js`)
Main alarm management system with features:
- Create, edit, and delete alarms
- Template-based alarm creation (weekday, weekend, study)
- Bulk alarm operations and CSV import
- Keyboard shortcuts (Alt+A, Alt+T, Alt+B)
- Snooze functionality
- Sound and notification support

**Key Methods:**
- `addAlarm(hour, minute, ampm, label, options)` - Create new alarm
- `removeAlarm(id)` - Delete alarm
- `toggleAlarm(id)` - Enable/disable alarm
- `applyTemplate(templateName)` - Apply predefined alarm sets
- `triggerAlarm(alarm)` - Handle alarm activation

### Data Service (`alarms/alarm-data-service.js`)
Handles data persistence and synchronization:
- Multi-tier storage (localStorage, cache, Firestore)
- Real-time sync across devices
- Offline support with automatic sync when online
- Conflict resolution for concurrent edits

**Key Features:**
- Automatic periodic sync every 5 minutes
- Debounced saves to prevent excessive writes
- Firebase authentication integration
- Cross-tab synchronization

### Clock Display (`schedule/clock-display.js`)
Real-time clock with customizable display:
- 12/24 hour format toggle
- Live time updates every second
- Date display with month, day, and date
- Responsive design

**API:**
```javascript
window.clockDisplay = {
    initializeClockDisplay,
    initializeTimeFormatToggle,
    updateClock
};
```

## Usage

### Basic Setup
```html
<!-- Include CSS files -->
<link href="features/sleep-management/ui/alarm-service.css" rel="stylesheet">
<link href="features/sleep-management/ui/sleep-saboteurs.css" rel="stylesheet">

<!-- Include JavaScript modules -->
<script src="features/sleep-management/alarms/alarm-service.js"></script>
<script src="features/sleep-management/schedule/clock-display.js"></script>
<script src="features/sleep-management/saboteurs/sleep-saboteurs-init.js"></script>
```

### Creating Alarms
```javascript
// Create a simple alarm
window.alarmService.addAlarm(7, 30, 'AM', 'Wake Up');

// Create recurring alarm with options
window.alarmService.addAlarm(9, 0, 'PM', 'Study Time', {
    recurring: true,
    days: [1, 2, 3, 4, 5], // Monday to Friday
    color: '#fe2c55',
    priority: 'high'
});

// Apply template
window.alarmService.applyTemplate('weekday');
```

### Clock Display
```javascript
// Initialize clock
window.clockDisplay.initializeClockDisplay();
window.clockDisplay.initializeTimeFormatToggle();
```

## Keyboard Shortcuts
- **Alt + A**: Quick add alarm
- **Alt + T**: Apply alarm templates
- **Alt + B**: Bulk add alarms
- **Alt + S**: Save (in modals)
- **Esc**: Cancel/close modals

## Data Storage
The module uses a three-tier storage approach:
1. **localStorage**: Immediate local storage for fast access
2. **Cache API**: Browser cache for offline support
3. **Firestore**: Cloud storage for cross-device sync

Data is automatically synchronized across all storage tiers with conflict resolution based on timestamps.

## Dependencies
- **Firebase**: Authentication and Firestore database
- **Bootstrap**: UI framework and icons
- **Web APIs**: Notifications, Service Workers, Cache API

## Browser Compatibility
- Modern browsers with ES6+ support
- Service Worker support for background notifications
- Notification API for alarm alerts
- Cache API for offline functionality

## Configuration
The module automatically configures itself but can be customized:
- Alarm templates can be modified in `alarm-service.js`
- Sync intervals can be adjusted in `alarm-data-service.js`
- UI themes can be customized via CSS variables

## Known Issues
- Service worker registration may need manual refresh in some browsers
- Notification permissions must be granted for alarm alerts
- Background alarms may not work if the browser tab is closed

## Future Enhancements
- Sleep quality tracking integration
- Smart wake-up based on sleep cycles
- Integration with fitness trackers
- Advanced sleep analytics and reporting
