# Study Spaces Feature

A comprehensive study location management system with image analysis, cloud storage, and timetable integration.

## 📁 Module Structure

```
features/study-spaces/
├── core/                   # Core study space management
│   └── studySpacesManager.js
├── analyzer/               # Space analysis and insights
│   └── studySpaceAnalyzer.js
├── firestore/              # Cloud storage integration
│   └── studySpacesFirestore.js
├── ui/                     # User interface components
│   ├── study-spaces.html
│   └── study-spaces.css
└── README.md              # This file
```

## 🚀 Features

### Core Functionality
- **Study Space Management**: Add, edit, and delete study spaces
- **Image Upload**: Drag & drop or click to upload study space images
- **Location Tracking**: Store and manage study space locations
- **Amenity Tracking**: Track available amenities (Wi-Fi, power outlets, etc.)
- **Cloud Sync**: Automatic synchronization across devices via Firebase

### Advanced Features
- **AI Analysis**: Analyze study space images for noise level, lighting, seating
- **Timetable Integration**: Upload and analyze class timetables
- **Schedule Analysis**: Get insights on free time and optimal study periods
- **Cross-tab Sync**: Real-time synchronization across browser tabs

## 🔧 API Reference

### StudySpacesManager Class

#### Constructor
```javascript
const manager = new StudySpacesManager();
```

#### Core Methods

##### `saveStudySpace()`
Saves a new study space with form data
```javascript
await manager.saveStudySpace();
```

##### `loadStudySpaces()`
Loads study spaces from Firestore or localStorage
```javascript
await manager.loadStudySpaces();
```

##### `displayStudySpaces()`
Renders study spaces in the UI
```javascript
manager.displayStudySpaces();
```

##### `saveSettings()`
Saves schedule and space settings
```javascript
await manager.saveSettings();
```

### Firestore Integration

#### `saveStudySpacesToFirestore(studySpaces)`
Saves study spaces to Firebase with image upload to Storage
```javascript
const success = await saveStudySpacesToFirestore(studySpaces);
```

#### `loadStudySpacesFromFirestore()`
Loads study spaces from Firebase with version control
```javascript
const spaces = await loadStudySpacesFromFirestore();
```

#### `deleteStudySpaceFromFirestore(spaceId)`
Deletes a study space and its associated image
```javascript
const success = await deleteStudySpaceFromFirestore(spaceId);
```

### Study Space Analyzer

#### `analyzeStudySpace(spaceId, file)`
Analyzes uploaded study space images using AI
```javascript
await analyzeStudySpace(spaceId, imageFile);
```

## 📊 Data Structure

### Study Space Object
```javascript
{
  id: Number,                    // Unique identifier
  name: String,                  // Space name
  location: String,              // Physical location
  description: String,           // Description
  amenities: Array,              // Available amenities
  image: String,                 // Base64 or URL
  imageStoragePath: String,      // Firebase Storage path
  createdAt: String,             // ISO timestamp
  lastModified: String,          // ISO timestamp
  userId: String                 // User identifier
}
```

### Schedule Object
```javascript
{
  wakeTime: String,              // Wake time (HH:MM)
  wakeBuffer: Number,            // Buffer in minutes
  sleepTime: String,             // Sleep time (HH:MM)
  sleepBuffer: Number,           // Buffer in minutes
  timetableImage: {
    path: String,                // Image path
    fileName: String,            // Original filename
    uploadTime: String           // Upload timestamp
  }
}
```

## 🔗 Dependencies

### External Dependencies
- **Firebase**: Authentication, Firestore, Storage
- **Bootstrap 5.3.2**: UI framework
- **Bootstrap Icons**: Icon library

### Internal Dependencies
- `../../../js/timetableAnalyzer.js` - Timetable analysis
- `../../../js/imageAnalyzer.js` - Image processing
- `../../../js/scheduleManager.js` - Schedule management
- `../../../js/sideDrawer.js` - Navigation
- `../../../js/firebaseAuth.js` - Authentication
- `../../../js/apiSettingsManager.js` - API configuration
- `../../../js/cross-tab-sync.js` - Cross-tab synchronization
- `../../../js/inject-header.js` - Header injection

## 🎯 Usage

### Basic Setup
1. Include the HTML file in your application
2. Ensure Firebase is configured
3. Initialize the StudySpacesManager

### Adding a Study Space
1. Click the upload area or drag & drop an image
2. Fill in the study space details form
3. Select applicable amenities
4. Click "Save Study Space"

### Timetable Analysis
1. Upload a class timetable image
2. Set wake and sleep times
3. View automated schedule analysis
4. Get study recommendations

## 🔒 Security

- All data is stored securely in Firebase Firestore
- Images are uploaded to Firebase Storage with user-specific paths
- Authentication required for cloud sync
- Local storage fallback for offline usage

## 🐛 Known Issues

- Script import paths may need adjustment based on deployment structure
- Image analysis requires external API configuration
- Cross-tab sync depends on localStorage events

## 🚀 Future Enhancements

- Location-based study space recommendations
- Integration with calendar apps
- Study session tracking
- Social features for sharing spaces
- Offline-first architecture improvements

## 📝 Notes

This module is designed to be self-contained and can run independently of other features. All dependencies are clearly defined and the module follows the modular architecture pattern established in the GPAce application.
