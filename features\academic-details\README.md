# Academic Details Module

## Overview
The Academic Details module provides comprehensive academic information management with support for subjects, grades, and semester organization. This module allows students to track their academic progress across multiple semesters with cloud synchronization capabilities.

## Features
- **Subject Management**: Create and manage academic subjects with credit hours and difficulty levels
- **Semester System**: Organize subjects by academic semesters with archiving support
- **Grade Tracking**: Track marks across different assessment categories (assignments, quizzes, midterms, finals)
- **Performance Analytics**: Calculate academic performance and GPA estimates
- **Cloud Sync**: Synchronize data with Firebase Firestore for cross-device access
- **Bulk Import**: Add multiple subjects at once using CSV-like format

## File Structure
```
features/academic-details/
├── subjects/
│   └── subject-management.js     # Subject creation and management
├── marks/
│   ├── subject-marks.js          # Core marks calculation logic
│   ├── subject-marks-ui.js       # Marks UI components
│   ├── subject-marks-integration.js # Global integration
│   └── marks-tracking.js         # Performance tracking
├── semester/
│   └── semester-management.js    # Semester organization
├── ui/
│   ├── academic-details.js       # Main application entry point
│   ├── academic-details.html     # Main UI page
│   ├── subject-marks.html        # Marks management page
│   ├── academic-details.css      # Main styles
│   └── subject-marks.css         # Marks page styles
└── README.md                     # This file
```

## API Reference

### Subject Management
- `generateTag(name)` - Generate unique tag for subject
- `createSubjectForms()` - Create subject input forms
- `saveSubjects()` - Save subjects to storage and cloud
- `displaySavedSubjects()` - Display saved subjects in UI
- `parseBulkInput()` - Process bulk subject input

### Marks Management
- `addSubjectMark(subjectTag, category, obtainedMarks, totalMarks, title)` - Add marks for a subject
- `getSubjectMarks(subjectTag)` - Retrieve all marks for a subject
- `updateSubjectPerformance(subjectTag)` - Calculate and update performance
- `setSubjectWeightages(subjectTag, weightages)` - Set assessment category weightages
- `getSubjectWeightages(subjectTag)` - Get current weightages

### Semester Management
- `updateSemesterSelector()` - Update semester dropdown options
- `handleSemesterChange()` - Handle semester switching
- `migrateToSemesterSystem()` - Migrate legacy data

## Usage Examples

### Adding Subjects
```javascript
// Bulk add subjects
const bulkData = "Calculus I, 3\nPhysics, 4\nChemistry, 3";
document.getElementById('bulkInput').value = bulkData;
parseBulkInput();
```

### Managing Marks
```javascript
// Add a quiz mark
addSubjectMark('CALC101', 'quiz', 85, 100, 'Quiz 1');

// Set weightages for a subject
setSubjectWeightages('CALC101', {
    assignment: 15,
    quiz: 10,
    midterm: 30,
    final: 40,
    revision: 5
});
```

### Semester Operations
```javascript
// Switch to a different semester
localStorage.setItem('currentAcademicSemester', 'Fall 2023');
updateSemesterSelector();
```

## Dependencies
- Firebase Firestore (for cloud sync)
- Bootstrap 5.3.2 (for UI components)
- Bootstrap Icons (for iconography)

## Integration Points
- **Firebase Services**: Uses `js/firestore.js` for cloud storage
- **Cross-tab Sync**: Integrates with `js/cross-tab-sync.js`
- **UI Utilities**: Uses `js/ui-utilities.js` for common functions
- **Theme Manager**: Supports light/dark theme switching

## Data Storage
- **Local Storage**: Primary storage for offline access
- **Firestore**: Cloud backup and synchronization
- **Storage Keys**:
  - `academicSemesters` - All semester data
  - `currentAcademicSemester` - Active semester
  - `academicSubjects` - Legacy format (backward compatibility)

## Known Issues
- Semester migration runs on every page load (should be optimized)
- Some duplicate HTML tags in subject-marks.html need cleanup
- Firebase initialization could be more robust

## Future Enhancements
- Export/import functionality for semester data
- Advanced analytics and reporting
- Integration with external academic systems
- Mobile-responsive improvements
- Offline-first architecture improvements
