<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Academic Details Module Test</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="./ui/academic-details.css" rel="stylesheet">
</head>
<body>
    <div class="container mt-4">
        <h1>Academic Details Module Test</h1>
        <div class="alert alert-info">
            <h4>Test Instructions</h4>
            <p>This page tests the academic details module functionality:</p>
            <ul>
                <li>Open browser console to see module loading messages</li>
                <li>Test subject creation and management</li>
                <li>Test semester switching</li>
                <li>Test marks tracking functionality</li>
            </ul>
        </div>

        <div class="row">
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        <h5>Module Status</h5>
                    </div>
                    <div class="card-body">
                        <div id="moduleStatus">
                            <p>Loading module status...</p>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        <h5>Quick Test</h5>
                    </div>
                    <div class="card-body">
                        <button class="btn btn-primary" onclick="testSubjectGeneration()">Test Subject Tag Generation</button>
                        <button class="btn btn-secondary" onclick="testLocalStorage()">Test Local Storage</button>
                        <div id="testResults" class="mt-3"></div>
                    </div>
                </div>
            </div>
        </div>

        <div class="mt-4">
            <h3>Navigation Links</h3>
            <a href="./ui/academic-details.html" class="btn btn-primary">Academic Details Main Page</a>
            <a href="./ui/subject-marks.html" class="btn btn-secondary">Subject Marks Page</a>
            <a href="../../../html/index.html" class="btn btn-outline-primary">Back to Main App</a>
        </div>
    </div>

    <script type="module">
        // Test module loading
        async function checkModuleStatus() {
            const statusDiv = document.getElementById('moduleStatus');
            let status = '<ul>';
            
            try {
                // Test if we can import the subject management module
                const subjectModule = await import('./subjects/subject-management.js');
                status += '<li class="text-success">✅ Subject Management module loaded</li>';
                
                // Test if functions are available
                if (typeof subjectModule.generateTag === 'function') {
                    status += '<li class="text-success">✅ generateTag function available</li>';
                } else {
                    status += '<li class="text-warning">⚠️ generateTag function not found</li>';
                }
                
            } catch (error) {
                status += `<li class="text-danger">❌ Subject Management module failed: ${error.message}</li>`;
            }

            try {
                // Test marks module
                const marksModule = await import('./marks/subject-marks-integration.js');
                status += '<li class="text-success">✅ Marks Integration module loaded</li>';
            } catch (error) {
                status += `<li class="text-danger">❌ Marks module failed: ${error.message}</li>`;
            }

            try {
                // Test semester module
                const semesterModule = await import('./semester/semester-management.js');
                status += '<li class="text-success">✅ Semester Management module loaded</li>';
            } catch (error) {
                status += `<li class="text-danger">❌ Semester module failed: ${error.message}</li>`;
            }

            status += '</ul>';
            statusDiv.innerHTML = status;
        }

        // Test functions
        window.testSubjectGeneration = async function() {
            const resultsDiv = document.getElementById('testResults');
            try {
                const { generateTag } = await import('./subjects/subject-management.js');
                const tag1 = generateTag('Calculus I');
                const tag2 = generateTag('Physics');
                
                resultsDiv.innerHTML = `
                    <div class="alert alert-success">
                        <h6>Tag Generation Test Results:</h6>
                        <p>Calculus I → ${tag1}</p>
                        <p>Physics → ${tag2}</p>
                    </div>
                `;
            } catch (error) {
                resultsDiv.innerHTML = `
                    <div class="alert alert-danger">
                        <h6>Tag Generation Test Failed:</h6>
                        <p>${error.message}</p>
                    </div>
                `;
            }
        };

        window.testLocalStorage = function() {
            const resultsDiv = document.getElementById('testResults');
            try {
                // Test localStorage access
                const testData = { test: 'academic-details-module', timestamp: new Date().toISOString() };
                localStorage.setItem('academic-details-test', JSON.stringify(testData));
                const retrieved = JSON.parse(localStorage.getItem('academic-details-test'));
                
                resultsDiv.innerHTML = `
                    <div class="alert alert-success">
                        <h6>Local Storage Test Results:</h6>
                        <p>Data stored and retrieved successfully</p>
                        <pre>${JSON.stringify(retrieved, null, 2)}</pre>
                    </div>
                `;
                
                // Clean up
                localStorage.removeItem('academic-details-test');
            } catch (error) {
                resultsDiv.innerHTML = `
                    <div class="alert alert-danger">
                        <h6>Local Storage Test Failed:</h6>
                        <p>${error.message}</p>
                    </div>
                `;
            }
        };

        // Initialize when page loads
        document.addEventListener('DOMContentLoaded', checkModuleStatus);
    </script>
</body>
</html>
